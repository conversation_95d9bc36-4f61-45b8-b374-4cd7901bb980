
#include "epoch_100_int8_new.h"  // The downloaded .h file

// Include EloquentTinyML
#include <tflm_esp32.h>
#include <eloquent_tinyml.h>

#define ARENA_SIZE 112134  // Adjust based on your model
#define INPUT_WIDTH 192    // Model input size is 192x192
#define INPUT_HEIGHT 192   // Model input size is 192x192
#define INPUT_CHANNELS 3   // RGB channels

// For EloquentTinyML v3.x, the correct way to declare the model
// Use the simpler constructor without template parameters
Eloquent::TF::Sequential model(ARENA_SIZE);

// Pseudo image data - simple test pattern
// This creates a 192x192x3 RGB image with a simple pattern for testing
// Memory usage: 192 * 192 * 3 = 110,592 bytes (~108 KB)
uint8_t pseudo_image[INPUT_WIDTH * INPUT_HEIGHT * INPUT_CHANNELS];

// Function to generate a pseudo image for testing
void generatePseudoImage() {
    Serial.println("Generating pseudo image...");

    for (int y = 0; y < INPUT_HEIGHT; y++) {
        for (int x = 0; x < INPUT_WIDTH; x++) {
            int idx = (y * INPUT_WIDTH + x) * INPUT_CHANNELS;

            // Create a simple test pattern
            // You can modify this to create different test patterns
            if (x < INPUT_WIDTH / 3) {
                // Red region
                pseudo_image[idx] = 255;     // R
                pseudo_image[idx + 1] = 0;   // G
                pseudo_image[idx + 2] = 0;   // B
            } else if (x < 2 * INPUT_WIDTH / 3) {
                // Green region
                pseudo_image[idx] = 0;       // R
                pseudo_image[idx + 1] = 255; // G
                pseudo_image[idx + 2] = 0;   // B
            } else {
                // Blue region
                pseudo_image[idx] = 0;       // R
                pseudo_image[idx + 1] = 0;   // G
                pseudo_image[idx + 2] = 255; // B
            }

            // Add some noise/pattern based on position
            if ((x + y) % 20 == 0) {
                pseudo_image[idx] = 128;
                pseudo_image[idx + 1] = 128;
                pseudo_image[idx + 2] = 128;
            }
        }
    }

    Serial.println("Pseudo image generated successfully!");
}

// Function to preprocess image for YOLO (normalize to 0-1 range)
void preprocessImage(uint8_t* input_image, float* output_tensor) {
    Serial.println("Preprocessing image...");

    for (int i = 0; i < INPUT_WIDTH * INPUT_HEIGHT * INPUT_CHANNELS; i++) {
        // Normalize pixel values from 0-255 to 0-1
        output_tensor[i] = input_image[i] / 255.0f;
    }

    Serial.println("Image preprocessing complete!");
}

// Function to run inference on the pseudo image
void runInference() {
    Serial.println("Starting inference...");

    // Get the actual input size from the model
    int model_input_size = model.inputSize();
    Serial.print("Model expects input size: ");
    Serial.println(model_input_size);

    // Create test input with the correct size
    float* test_input = new float[model_input_size];

    if (test_input == nullptr) {
        Serial.println("Failed to allocate memory for test input!");
        return;
    }

    // Fill with test data
    for (int i = 0; i < model_input_size; i++) {
        test_input[i] = 0.5f;  // Simple test values
    }

    Serial.println("Running inference with test input...");

    // Run inference
    unsigned long start_time = millis();

    // For EloquentTinyML v3.x, predict returns a float value
    float prediction = model.predict(test_input);
    unsigned long inference_time = millis() - start_time;

    Serial.print("Inference completed in: ");
    Serial.print(inference_time);
    Serial.println(" ms");

    // Check if prediction is valid (not NaN)
    if (!isnan(prediction)) {
        Serial.println("Inference successful!");
        Serial.print("Prediction result: ");
        Serial.println(prediction, 6);

        // Print model information
        Serial.print("Model input size: ");
        Serial.println(model.inputSize());
        Serial.print("Model output size: ");
        Serial.println(model.outputSize());

        // Try to access the full output tensor
        float* output = model.output();
        if (output != nullptr) {
            Serial.println("First 5 output values:");
            for (int i = 0; i < min(5, model.outputSize()); i++) {
                Serial.print("Output[");
                Serial.print(i);
                Serial.print("]: ");
                Serial.println(output[i], 6);
            }
        }

        Serial.println("Basic inference test passed!");

    } else {
        Serial.println("Inference failed - prediction is NaN!");
        Serial.print("Error: ");
        Serial.println(model.exception.toString());
    }

    // Clean up
    delete[] test_input;

    Serial.println("Inference test complete!\n");
}

// Function to run inference with full pseudo image (call this after basic test works)
void runFullInference() {
    Serial.println("Starting full image inference...");

    // Check if the model expects the right input size
    int expected_input_size = INPUT_WIDTH * INPUT_HEIGHT * INPUT_CHANNELS;
    int model_input_size = model.inputSize();

    Serial.print("Expected input size: ");
    Serial.println(expected_input_size);
    Serial.print("Model input size: ");
    Serial.println(model_input_size);

    if (expected_input_size != model_input_size) {
        Serial.println("Warning: Input size mismatch!");
        Serial.println("Using model's expected input size...");
        expected_input_size = model_input_size;
    }

    Serial.print("Memory required: ");
    Serial.print((expected_input_size * 4) / 1024);
    Serial.println(" KB");

    // Check available heap before allocation
    Serial.print("Free heap before allocation: ");
    Serial.print(ESP.getFreeHeap());
    Serial.println(" bytes");

    // Allocate memory for preprocessed input
    float* input_tensor = new float[expected_input_size];

    if (input_tensor == nullptr) {
        Serial.println("Failed to allocate memory for input tensor!");
        return;
    }

    Serial.print("Free heap after allocation: ");
    Serial.print(ESP.getFreeHeap());
    Serial.println(" bytes");

    // Preprocess the pseudo image
    if (expected_input_size == INPUT_WIDTH * INPUT_HEIGHT * INPUT_CHANNELS) {
        preprocessImage(pseudo_image, input_tensor);
    } else {
        // Fill with test data if size doesn't match
        for (int i = 0; i < expected_input_size; i++) {
            input_tensor[i] = 0.5f;
        }
    }

    // Run inference
    unsigned long start_time = millis();

    float prediction = model.predict(input_tensor);
    unsigned long inference_time = millis() - start_time;

    Serial.print("Full inference completed in: ");
    Serial.print(inference_time);
    Serial.println(" ms");

    if (!isnan(prediction)) {
        Serial.println("Full image inference successful!");
        Serial.print("Main prediction: ");
        Serial.println(prediction, 6);

        // Try to access the full output tensor
        float* output = model.output();
        if (output != nullptr) {
            int output_size = model.outputSize();
            Serial.print("Output tensor size: ");
            Serial.println(output_size);

            Serial.println("First 10 output values:");
            for (int i = 0; i < min(10, output_size); i++) {
                Serial.print("Output[");
                Serial.print(i);
                Serial.print("]: ");
                Serial.println(output[i], 6);
            }

            // Process YOLO output
            processYOLOOutput(output, output_size);
        } else {
            Serial.println("Could not access output tensor");
            processYOLOOutput(prediction);
        }

    } else {
        Serial.println("Full inference failed!");
        Serial.print("Error: ");
        Serial.println(model.exception.toString());
    }

    // Clean up
    delete[] input_tensor;

    Serial.print("Free heap after cleanup: ");
    Serial.print(ESP.getFreeHeap());
    Serial.println(" bytes");

    Serial.println("Full inference complete!\n");
}

// Function to process YOLO output (simplified version)
void processYOLOOutput(float* output, int output_size) {
    Serial.println("Processing YOLO output...");

    // This is a simplified version - actual YOLO post-processing is more complex
    // and depends on your specific model architecture

    float confidence_threshold = 0.5;
    int detections_found = 0;

    // For demonstration, let's assume the output contains confidence scores
    // In a real YOLO model, you'd need to parse the output format properly
    for (int i = 0; i < min(100, output_size); i++) {
        if (output[i] > confidence_threshold) {
            detections_found++;
            Serial.print("Detection ");
            Serial.print(detections_found);
            Serial.print(" - Confidence: ");
            Serial.println(output[i], 4);
        }
    }

    Serial.print("Total detections above threshold (");
    Serial.print(confidence_threshold);
    Serial.print("): ");
    Serial.println(detections_found);
}

// Overloaded version for single prediction value
void processYOLOOutput(float prediction) {
    Serial.println("Processing single YOLO prediction...");
    Serial.print("Prediction value: ");
    Serial.println(prediction, 6);

    // For a single output, you might interpret it as a classification score
    // or the highest confidence detection
    if (prediction > 0.5) {
        Serial.println("High confidence prediction detected!");
    } else {
        Serial.println("Low confidence prediction.");
    }
}

void setup() {
    Serial.begin(115200);
    delay(2000); // Give time for serial monitor to connect

    Serial.println("ESP32-S3 YOLO TensorFlow Lite Test");
    Serial.println("===================================");

    // Generate pseudo image for testing
    generatePseudoImage();

    Serial.println("Initializing TensorFlow Lite model...");

    // Initialize model with the data from your .h file
    while (!model.begin(epoch_100_int8).isOk()) {
        Serial.print("Model initialization failed: ");
        Serial.println(model.exception.toString());
        delay(1000);
    }

    Serial.println("Model loaded successfully!");

    // Print model information
    Serial.print("Input size: ");
    Serial.println(model.inputSize());
    Serial.print("Output size: ");
    Serial.println(model.outputSize());

    Serial.println("Setup complete! Starting inference loop...\n");
}

void loop() {
    // Start with basic inference test
    runInference();

    // Uncomment the line below once basic inference works
    // runFullInference();

    // Wait 10 seconds before next inference
    delay(10000);
}

// Commented out original code for reference:
/*
// #include "epoch_100_int8_new.h"  // The downloaded .h file

// // Include EloquentTinyML
// #include <tflm_esp32.h>
// #include <eloquent_tinyml.h>

// #define ARENA_SIZE 112134  // Adjust based on your model

// Eloquent::TF::Sequential<TF_NUM_OPS, ARENA_SIZE> model;

// void setup() {
//     Serial.begin(115200);

//     // Initialize model with the data from your .h file
//     while (!model.begin(epoch_100_int8).isOk()) {
//         Serial.println(model.exception.toString());
//         delay(1000);
//     }

//     Serial.println("Model loaded!");
// }

// void loop()
// {


// }
*/