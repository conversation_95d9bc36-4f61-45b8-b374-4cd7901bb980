

// #include "epoch_100_int8_new.h"  // The downloaded .h file

// // Include EloquentTinyML
// #include <tflm_esp32.h>
// #include <eloquent_tinyml.h>

// #define ARENA_SIZE 112134  // Adjust based on your model

// Eloquent::TF::Sequential<TF_NUM_OPS, ARENA_SIZE> model;

// void setup() {
//     Serial.begin(115200);
    
//     // Initialize model with the data from your .h file
//     while (!model.begin(epoch_100_int8).isOk()) {
//         Serial.println(model.exception.toString());
//         delay(1000);
//     }
    
//     Serial.println("Model loaded!");
// }

// void loop()
// {


// }