
#include "epoch_100_int8_new.h"  // The downloaded .h file

// Include EloquentTinyML
#include <tflm_esp32.h>
#include <eloquent_tinyml.h>

#define ARENA_SIZE 112134  // Adjust based on your model
#define INPUT_WIDTH 192    // Model input size is 192x192
#define INPUT_HEIGHT 192   // Model input size is 192x192
#define INPUT_CHANNELS 3   // RGB channels

// For EloquentTinyML v3.x, use the correct template parameters
// You may need to adjust TF_NUM_OPS based on your model
#ifndef TF_NUM_OPS
#define TF_NUM_OPS 100  // Default value, adjust if needed
#endif

// Model instance - using the correct syntax for v3.x
Eloquent::TF::Sequential<TF_NUM_OPS, ARENA_SIZE> model;

// Pseudo image data - simple test pattern
// This creates a 192x192x3 RGB image with a simple pattern for testing
// Memory usage: 192 * 192 * 3 = 110,592 bytes (~108 KB)
uint8_t pseudo_image[INPUT_WIDTH * INPUT_HEIGHT * INPUT_CHANNELS];

// Function to generate a pseudo image for testing
void generatePseudoImage() {
    Serial.println("Generating pseudo image...");

    for (int y = 0; y < INPUT_HEIGHT; y++) {
        for (int x = 0; x < INPUT_WIDTH; x++) {
            int idx = (y * INPUT_WIDTH + x) * INPUT_CHANNELS;

            // Create a simple test pattern
            // You can modify this to create different test patterns
            if (x < INPUT_WIDTH / 3) {
                // Red region
                pseudo_image[idx] = 255;     // R
                pseudo_image[idx + 1] = 0;   // G
                pseudo_image[idx + 2] = 0;   // B
            } else if (x < 2 * INPUT_WIDTH / 3) {
                // Green region
                pseudo_image[idx] = 0;       // R
                pseudo_image[idx + 1] = 255; // G
                pseudo_image[idx + 2] = 0;   // B
            } else {
                // Blue region
                pseudo_image[idx] = 0;       // R
                pseudo_image[idx + 1] = 0;   // G
                pseudo_image[idx + 2] = 255; // B
            }

            // Add some noise/pattern based on position
            if ((x + y) % 20 == 0) {
                pseudo_image[idx] = 128;
                pseudo_image[idx + 1] = 128;
                pseudo_image[idx + 2] = 128;
            }
        }
    }

    Serial.println("Pseudo image generated successfully!");
}

// Function to preprocess image for YOLO (normalize to 0-1 range)
void preprocessImage(uint8_t* input_image, float* output_tensor) {
    Serial.println("Preprocessing image...");

    for (int i = 0; i < INPUT_WIDTH * INPUT_HEIGHT * INPUT_CHANNELS; i++) {
        // Normalize pixel values from 0-255 to 0-1
        output_tensor[i] = input_image[i] / 255.0f;
    }

    Serial.println("Image preprocessing complete!");
}

// Function to run inference on the pseudo image
void runInference() {
    Serial.println("Starting inference...");

    // Create a smaller test input for initial testing
    // Start with a much smaller input to test basic functionality
    const int test_input_size = 10;  // Small test input
    float test_input[test_input_size];

    // Fill with test data
    for (int i = 0; i < test_input_size; i++) {
        test_input[i] = 0.5f;  // Simple test values
    }

    Serial.println("Running inference with test input...");

    // Run inference
    unsigned long start_time = millis();

    // Try the predict method - this should work with EloquentTinyML v3.x
    if (model.predict(test_input)) {
        unsigned long inference_time = millis() - start_time;

        Serial.print("Inference completed in: ");
        Serial.print(inference_time);
        Serial.println(" ms");

        // Try to get the prediction result
        Serial.println("Inference successful!");

        // For EloquentTinyML v3.x, try to access outputs
        Serial.print("Model input size: ");
        Serial.println(model.inputSize());
        Serial.print("Model output size: ");
        Serial.println(model.outputSize());

        // Simple success message for now
        Serial.println("Basic inference test passed!");

    } else {
        Serial.println("Inference failed!");
        if (model.exception.toString() != nullptr) {
            Serial.print("Error: ");
            Serial.println(model.exception.toString());
        }
    }

    Serial.println("Inference test complete!\n");
}

// Function to run inference with full pseudo image (call this after basic test works)
void runFullInference() {
    Serial.println("Starting full image inference...");
    Serial.print("Input tensor size: ");
    Serial.print(INPUT_WIDTH * INPUT_HEIGHT * INPUT_CHANNELS);
    Serial.println(" floats");
    Serial.print("Memory required: ");
    Serial.print((INPUT_WIDTH * INPUT_HEIGHT * INPUT_CHANNELS * 4) / 1024);
    Serial.println(" KB");

    // Check available heap before allocation
    Serial.print("Free heap before allocation: ");
    Serial.print(ESP.getFreeHeap());
    Serial.println(" bytes");

    // Allocate memory for preprocessed input
    float* input_tensor = new float[INPUT_WIDTH * INPUT_HEIGHT * INPUT_CHANNELS];

    if (input_tensor == nullptr) {
        Serial.println("Failed to allocate memory for input tensor!");
        return;
    }

    Serial.print("Free heap after allocation: ");
    Serial.print(ESP.getFreeHeap());
    Serial.println(" bytes");

    // Preprocess the pseudo image
    preprocessImage(pseudo_image, input_tensor);

    // Run inference
    unsigned long start_time = millis();

    if (model.predict(input_tensor)) {
        unsigned long inference_time = millis() - start_time;

        Serial.print("Full inference completed in: ");
        Serial.print(inference_time);
        Serial.println(" ms");

        Serial.println("Full image inference successful!");

        // Try to access outputs if available
        Serial.print("Model output size: ");
        Serial.println(model.outputSize());

    } else {
        Serial.println("Full inference failed!");
        if (model.exception.toString() != nullptr) {
            Serial.print("Error: ");
            Serial.println(model.exception.toString());
        }
    }

    // Clean up
    delete[] input_tensor;

    Serial.print("Free heap after cleanup: ");
    Serial.print(ESP.getFreeHeap());
    Serial.println(" bytes");

    Serial.println("Full inference complete!\n");
}

// Function to process YOLO output (simplified version)
void processYOLOOutput(float* output, int output_size) {
    Serial.println("Processing YOLO output...");

    // This is a simplified version - actual YOLO post-processing is more complex
    // and depends on your specific model architecture

    float confidence_threshold = 0.5;
    int detections_found = 0;

    // For demonstration, let's assume the output contains confidence scores
    // In a real YOLO model, you'd need to parse the output format properly
    for (int i = 0; i < min(100, output_size); i++) {
        if (output[i] > confidence_threshold) {
            detections_found++;
            Serial.print("Detection ");
            Serial.print(detections_found);
            Serial.print(" - Confidence: ");
            Serial.println(output[i], 4);
        }
    }

    Serial.print("Total detections above threshold (");
    Serial.print(confidence_threshold);
    Serial.print("): ");
    Serial.println(detections_found);
}

// Overloaded version for single prediction value
void processYOLOOutput(float prediction) {
    Serial.println("Processing single YOLO prediction...");
    Serial.print("Prediction value: ");
    Serial.println(prediction, 6);

    // For a single output, you might interpret it as a classification score
    // or the highest confidence detection
    if (prediction > 0.5) {
        Serial.println("High confidence prediction detected!");
    } else {
        Serial.println("Low confidence prediction.");
    }
}

void setup() {
    Serial.begin(115200);
    delay(2000); // Give time for serial monitor to connect

    Serial.println("ESP32-S3 YOLO TensorFlow Lite Test");
    Serial.println("===================================");

    // Generate pseudo image for testing
    generatePseudoImage();

    Serial.println("Initializing TensorFlow Lite model...");

    // Initialize model with the data from your .h file
    while (!model.begin(epoch_100_int8).isOk()) {
        Serial.print("Model initialization failed: ");
        Serial.println(model.exception.toString());
        delay(1000);
    }

    Serial.println("Model loaded successfully!");

    // Print model information
    Serial.print("Input size: ");
    Serial.println(model.inputSize());
    Serial.print("Output size: ");
    Serial.println(model.outputSize());

    Serial.println("Setup complete! Starting inference loop...\n");
}

void loop() {
    // Start with basic inference test
    runInference();

    // Uncomment the line below once basic inference works
    // runFullInference();

    // Wait 10 seconds before next inference
    delay(10000);
}

// Commented out original code for reference:
/*
// #include "epoch_100_int8_new.h"  // The downloaded .h file

// // Include EloquentTinyML
// #include <tflm_esp32.h>
// #include <eloquent_tinyml.h>

// #define ARENA_SIZE 112134  // Adjust based on your model

// Eloquent::TF::Sequential<TF_NUM_OPS, ARENA_SIZE> model;

// void setup() {
//     Serial.begin(115200);

//     // Initialize model with the data from your .h file
//     while (!model.begin(epoch_100_int8).isOk()) {
//         Serial.println(model.exception.toString());
//         delay(1000);
//     }

//     Serial.println("Model loaded!");
// }

// void loop()
// {


// }
*/